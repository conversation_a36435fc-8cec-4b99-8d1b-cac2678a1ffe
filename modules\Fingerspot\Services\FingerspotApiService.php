<?php

namespace Modules\Fingerspot\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Exception;

class FingerspotApiService
{
    protected $client;
    protected $baseUrl;
    protected $apiKey;
    protected $secretKey;
    protected $timeout;
    protected $retryAttempts;
    protected $retryDelay;

    public function __construct()
    {
        $this->baseUrl = config('fingerspot.api.base_url', 'https://developer.fingerspot.io/api');
        $this->apiKey = config('fingerspot.auth.api_key');
        $this->secretKey = config('fingerspot.auth.secret_key');
        $this->timeout = config('fingerspot.api.timeout', 30);
        $this->retryAttempts = config('fingerspot.api.retry_attempts', 3);
        $this->retryDelay = config('fingerspot.api.retry_delay', 1000);

        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => $this->timeout,
            'verify' => false,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-API-Key' => $this->apiKey,
                'User-Agent' => 'Laravel-Fingerspot-Module/1.0'
            ]
        ]);
    }

    /**
     * Generate authentication signature
     */
    protected function generateSignature($method, $endpoint, $timestamp, $body = '')
    {
        $stringToSign = $method . "\n" . $endpoint . "\n" . $timestamp . "\n" . $body;
        return hash_hmac('sha256', $stringToSign, $this->secretKey);
    }

    /**
     * Make authenticated API request with retry logic
     */
    protected function makeRequest($method, $endpoint, $data = [], $options = [])
    {
        $attempt = 0;
        
        while ($attempt < $this->retryAttempts) {
            try {
                $timestamp = time();
                $body = !empty($data) ? json_encode($data) : '';
                $signature = $this->generateSignature($method, $endpoint, $timestamp, $body);

                $headers = [
                    'X-Timestamp' => $timestamp,
                    'X-Signature' => $signature,
                ];

                $requestOptions = array_merge([
                    'headers' => $headers,
                ], $options);

                if (!empty($data)) {
                    $requestOptions['json'] = $data;
                }

                $response = $this->client->request($method, $endpoint, $requestOptions);
                $responseBody = $response->getBody()->getContents();

                $result = [
                    'success' => true,
                    'status_code' => $response->getStatusCode(),
                    'data' => json_decode($responseBody, true),
                    'raw_response' => $responseBody,
                    'attempt' => $attempt + 1
                ];

                // Log successful request if debug is enabled
                if (config('fingerspot.debug')) {
                    $this->logDebug("API request successful: {$method} {$endpoint}", $result);
                }

                return $result;

            } catch (RequestException $e) {
                $attempt++;
                
                if ($attempt >= $this->retryAttempts) {
                    $this->logError("API request failed after {$this->retryAttempts} attempts: {$method} {$endpoint}", $e);
                    
                    $response = $e->getResponse();
                    $statusCode = $response ? $response->getStatusCode() : 0;
                    $responseBody = $response ? $response->getBody()->getContents() : '';

                    return [
                        'success' => false,
                        'status_code' => $statusCode,
                        'error' => $e->getMessage(),
                        'response' => $responseBody ? json_decode($responseBody, true) : null,
                        'attempts' => $attempt
                    ];
                }
                
                // Wait before retry
                usleep($this->retryDelay * 1000);
            } catch (Exception $e) {
                $this->logError("API request exception: {$method} {$endpoint}", $e);
                
                return [
                    'success' => false,
                    'status_code' => 0,
                    'error' => $e->getMessage(),
                    'response' => null,
                    'attempts' => $attempt + 1
                ];
            }
        }
    }

    /**
     * Get device list
     */
    public function getDevices($page = 1, $limit = 50)
    {
        $cacheKey = "fingerspot_devices_{$page}_{$limit}";
        
        if (config('fingerspot.features.api_caching')) {
            return $this->getCachedData($cacheKey, function () use ($page, $limit) {
                return $this->makeRequest('GET', '/devices', [], [
                    'query' => [
                        'page' => $page,
                        'limit' => $limit
                    ]
                ]);
            });
        }
        
        return $this->makeRequest('GET', '/devices', [], [
            'query' => [
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }

    /**
     * Get device information
     */
    public function getDevice($deviceId)
    {
        $cacheKey = "fingerspot_device_{$deviceId}";
        
        if (config('fingerspot.features.api_caching')) {
            return $this->getCachedData($cacheKey, function () use ($deviceId) {
                return $this->makeRequest('GET', "/devices/{$deviceId}");
            }, 60); // Cache for 1 minute
        }
        
        return $this->makeRequest('GET', "/devices/{$deviceId}");
    }

    /**
     * Get device status
     */
    public function getDeviceStatus($deviceId)
    {
        return $this->makeRequest('GET', "/devices/{$deviceId}/status");
    }

    /**
     * Get attendance logs
     */
    public function getAttendanceLogs($deviceId = null, $startDate = null, $endDate = null, $page = 1, $limit = 100)
    {
        $params = [
            'page' => $page,
            'limit' => $limit
        ];

        if ($deviceId) {
            $params['device_id'] = $deviceId;
        }

        if ($startDate) {
            $params['start_date'] = $startDate;
        }

        if ($endDate) {
            $params['end_date'] = $endDate;
        }

        return $this->makeRequest('GET', '/attendance/logs', [], [
            'query' => $params
        ]);
    }

    /**
     * Get user/employee list
     */
    public function getUsers($deviceId = null, $page = 1, $limit = 100)
    {
        $params = [
            'page' => $page,
            'limit' => $limit
        ];

        if ($deviceId) {
            $params['device_id'] = $deviceId;
        }

        return $this->makeRequest('GET', '/users', [], [
            'query' => $params
        ]);
    }

    /**
     * Create or update user
     */
    public function createUser($userData)
    {
        return $this->makeRequest('POST', '/users', $userData);
    }

    /**
     * Update user
     */
    public function updateUser($userId, $userData)
    {
        return $this->makeRequest('PUT', "/users/{$userId}", $userData);
    }

    /**
     * Delete user
     */
    public function deleteUser($userId)
    {
        return $this->makeRequest('DELETE', "/users/{$userId}");
    }

    /**
     * Sync user to device
     */
    public function syncUserToDevice($deviceId, $userId)
    {
        return $this->makeRequest('POST', "/devices/{$deviceId}/users/{$userId}/sync");
    }

    /**
     * Get device logs
     */
    public function getDeviceLogs($deviceId, $startDate = null, $endDate = null, $page = 1, $limit = 100)
    {
        $params = [
            'page' => $page,
            'limit' => $limit
        ];

        if ($startDate) {
            $params['start_date'] = $startDate;
        }

        if ($endDate) {
            $params['end_date'] = $endDate;
        }

        return $this->makeRequest('GET', "/devices/{$deviceId}/logs", [], [
            'query' => $params
        ]);
    }

    /**
     * Send command to device
     */
    public function sendDeviceCommand($deviceId, $command, $parameters = [])
    {
        return $this->makeRequest('POST', "/devices/{$deviceId}/commands", [
            'command' => $command,
            'parameters' => $parameters
        ]);
    }

    /**
     * Get webhook configurations
     */
    public function getWebhooks()
    {
        return $this->makeRequest('GET', '/webhooks');
    }

    /**
     * Create webhook
     */
    public function createWebhook($webhookData)
    {
        return $this->makeRequest('POST', '/webhooks', $webhookData);
    }

    /**
     * Update webhook
     */
    public function updateWebhook($webhookId, $webhookData)
    {
        return $this->makeRequest('PUT', "/webhooks/{$webhookId}", $webhookData);
    }

    /**
     * Delete webhook
     */
    public function deleteWebhook($webhookId)
    {
        return $this->makeRequest('DELETE', "/webhooks/{$webhookId}");
    }

    /**
     * Test webhook
     */
    public function testWebhook($webhookId)
    {
        return $this->makeRequest('POST', "/webhooks/{$webhookId}/test");
    }

    /**
     * Get API usage statistics
     */
    public function getApiUsage($startDate = null, $endDate = null)
    {
        $params = [];

        if ($startDate) {
            $params['start_date'] = $startDate;
        }

        if ($endDate) {
            $params['end_date'] = $endDate;
        }

        return $this->makeRequest('GET', '/usage', [], [
            'query' => $params
        ]);
    }

    /**
     * Test API connectivity
     */
    public function testConnectivity()
    {
        $startTime = microtime(true);
        
        try {
            $result = $this->makeRequest('GET', '/health');
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);
            
            return [
                'connected' => $result['success'],
                'status' => $result['success'] ? 'online' : 'offline',
                'response_time' => $responseTime . 'ms',
                'last_check' => now()->toDateTimeString(),
                'api_response' => $result
            ];
        } catch (Exception $e) {
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2);
            
            return [
                'connected' => false,
                'status' => 'offline',
                'response_time' => $responseTime . 'ms',
                'last_check' => now()->toDateTimeString(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get module configuration
     */
    public function getConfiguration()
    {
        return [
            'module_name' => 'Fingerspot',
            'api_base_url' => $this->baseUrl,
            'api_version' => config('fingerspot.api.version'),
            'timeout' => $this->timeout,
            'retry_attempts' => $this->retryAttempts,
            'features' => config('fingerspot.features'),
            'webhook_enabled' => config('fingerspot.webhook.enabled'),
            'debug_mode' => config('fingerspot.debug'),
            'cache_enabled' => config('fingerspot.features.api_caching'),
            'cache_ttl' => config('fingerspot.cache_ttl'),
        ];
    }

    /**
     * Log error messages
     */
    protected function logError($message, Exception $exception)
    {
        if (config('fingerspot.logging.enabled')) {
            Log::channel(config('fingerspot.logging.channel', 'single'))
               ->error("[FingerspotAPI] {$message}", [
                   'exception' => $exception->getMessage(),
                   'trace' => $exception->getTraceAsString(),
                   'api_key' => $this->apiKey ? substr($this->apiKey, 0, 8) . '...' : 'not_set',
               ]);
        }
    }

    /**
     * Log debug messages
     */
    protected function logDebug($message, $context = [])
    {
        if (config('fingerspot.logging.enabled') && config('fingerspot.debug')) {
            Log::channel(config('fingerspot.logging.channel', 'single'))
               ->debug("[FingerspotAPI] {$message}", $context);
        }
    }

    /**
     * Get cached data or fetch from API
     */
    public function getCachedData($cacheKey, $callback, $ttl = null)
    {
        $ttl = $ttl ?? config('fingerspot.cache_ttl', 300);
        return Cache::remember($cacheKey, $ttl, $callback);
    }

    /**
     * Clear specific cache
     */
    public function clearCache($pattern = 'fingerspot_*')
    {
        Cache::flush();
    }
}
